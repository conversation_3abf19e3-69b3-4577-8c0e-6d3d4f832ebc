#!/usr/bin/env python3
"""
简单测试脚本 - 不启动GUI
"""

def test_basic_imports():
    """测试基础导入"""
    print("🔍 测试基础导入...")
    
    try:
        import main_window
        print("✅ main_window.py 导入成功")
        
        # 测试类定义
        if hasattr(main_window, 'MainWindow'):
            print("✅ MainWindow 类定义正常")
        
        if hasattr(main_window, 'ChartWidget'):
            print("✅ ChartWidget 类可用")
            
        if hasattr(main_window, 'CHARTS_AVAILABLE'):
            charts_status = "可用" if main_window.CHARTS_AVAILABLE else "不可用(使用回退模式)"
            print(f"📊 图表功能: {charts_status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_dependencies():
    """测试依赖库"""
    print("\n📦 测试依赖库...")
    
    dependencies = {
        'ccxt': '交易所API',
        'pandas': '数据处理',
        'talib': '技术指标',
        'requests': 'HTTP请求',
        'numpy': '数值计算'
    }
    
    success_count = 0
    for dep, desc in dependencies.items():
        try:
            __import__(dep)
            print(f"✅ {dep} ({desc}) - 可用")
            success_count += 1
        except ImportError:
            print(f"❌ {dep} ({desc}) - 不可用")
    
    print(f"\n📊 依赖库状态: {success_count}/{len(dependencies)} 可用")
    return success_count == len(dependencies)

def check_config_files():
    """检查配置文件"""
    print("\n📁 检查配置文件...")
    
    import os
    
    config_files = [
        '.env',
        'config.json',
        'trading_settings.json',
        'indicator_settings.json'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ {config_file} - 存在")
        else:
            print(f"⚠️  {config_file} - 不存在 (将使用默认设置)")

def main():
    """主函数"""
    print("🚀 币安量化交易机器人 - 简单测试")
    print("=" * 50)
    
    # 测试基础导入
    import_ok = test_basic_imports()
    
    # 测试依赖
    deps_ok = test_dependencies()
    
    # 检查配置
    check_config_files()
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    
    if import_ok:
        print("✅ 主程序导入正常")
    else:
        print("❌ 主程序导入失败")
        
    if deps_ok:
        print("✅ 所有依赖库可用")
    else:
        print("⚠️  部分依赖库缺失")
    
    print("\n💡 建议:")
    if import_ok:
        print("1. 代码修复成功，主要功能可用")
        print("2. 图表功能使用简化模式（PyQt6-Charts问题）")
        print("3. 可以尝试运行程序进行交易")
        print("\n🚀 启动命令:")
        print("   python main_window.py")
        
        if not deps_ok:
            print("\n📦 安装缺失依赖:")
            print("   pip install ccxt pandas talib requests python-dotenv numpy")
    else:
        print("1. 需要先修复PyQt6安装问题")
        print("2. 运行修复脚本: python fix_pyqt6.py")

if __name__ == "__main__":
    main()
