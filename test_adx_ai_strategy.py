#!/usr/bin/env python3
"""
测试ADX策略在AI交易中的应用
验证ADX持续性分析是否正确应用于AI交易决策
"""

import os
import sys
import numpy as np
from datetime import datetime

def test_adx_strategy_logic():
    """测试ADX策略逻辑"""
    print("🧪 测试ADX策略逻辑...")
    
    # 模拟ADX持续性分析函数
    def mock_check_adx_trend_persistence(adx_values, plus_di_values, minus_di_values, min_periods=3):
        """模拟ADX趋势持续性检测"""
        
        # 检查数据长度
        if len(adx_values) < min_periods + 1:
            return {
                'signal_type': 'insufficient_data',
                'signal_strength': 0,
                'persistence_periods': 0,
                'description': '数据不足，无法判断趋势持续性'
            }
        
        # 检查ADX上升趋势
        adx_rising_periods = 0
        for i in range(len(adx_values) - min_periods, len(adx_values)):
            if i > 0 and adx_values[i] > adx_values[i-1]:
                adx_rising_periods += 1
            else:
                break
        
        # 分析+DI趋势
        plus_di_trend = analyze_di_trend(plus_di_values, min_periods)
        minus_di_trend = analyze_di_trend(minus_di_values, min_periods)
        
        # 当前值
        current_adx = adx_values[-1]
        current_plus_di = plus_di_values[-1]
        current_minus_di = minus_di_values[-1]
        
        # ADX阈值
        adx_threshold = 25
        
        if current_adx < adx_threshold:
            return {
                'signal_type': 'weak_trend',
                'signal_strength': 0,
                'persistence_periods': 0,
                'description': f'ADX({current_adx:.2f})低于阈值({adx_threshold})，趋势不够强'
            }
        
        # 情况1：强烈空头信号 - ADX上升 + +DI持续下跌 + -DI持续上升
        if (adx_rising_periods >= min_periods and
            plus_di_trend['direction'] == 'falling' and
            minus_di_trend['direction'] == 'rising' and
            current_minus_di > current_plus_di):
            
            persistence_score = min(plus_di_trend['persistence_periods'], minus_di_trend['persistence_periods'])
            adx_strength_score = min(adx_rising_periods, 5)
            di_change_score = (plus_di_trend['total_change'] + minus_di_trend['total_change']) / 2
            signal_strength = persistence_score + adx_strength_score + min(di_change_score, 3)
            
            return {
                'signal_type': 'strong_bearish',
                'signal_strength': signal_strength,
                'persistence_periods': persistence_score,
                'description': f'强烈空头信号：ADX持续上升{adx_rising_periods}期，+DI持续下跌{plus_di_trend["persistence_periods"]}期，-DI持续上升{minus_di_trend["persistence_periods"]}期'
            }
        
        # 情况2：强烈多头信号 - ADX上升 + +DI持续上升 + -DI持续下跌
        elif (adx_rising_periods >= min_periods and
              plus_di_trend['direction'] == 'rising' and
              minus_di_trend['direction'] == 'falling' and
              current_plus_di > current_minus_di):
            
            persistence_score = min(plus_di_trend['persistence_periods'], minus_di_trend['persistence_periods'])
            adx_strength_score = min(adx_rising_periods, 5)
            di_change_score = (plus_di_trend['total_change'] + minus_di_trend['total_change']) / 2
            signal_strength = persistence_score + adx_strength_score + min(di_change_score, 3)
            
            return {
                'signal_type': 'strong_bullish',
                'signal_strength': signal_strength,
                'persistence_periods': persistence_score,
                'description': f'强烈多头信号：ADX持续上升{adx_rising_periods}期，+DI持续上升{plus_di_trend["persistence_periods"]}期，-DI持续下跌{minus_di_trend["persistence_periods"]}期'
            }
        
        # 情况3：趋势减弱信号
        elif (plus_di_trend['direction'] == 'falling' and minus_di_trend['direction'] == 'falling'):
            return {
                'signal_type': 'weakening_trend',
                'signal_strength': -1,
                'persistence_periods': min(plus_di_trend['persistence_periods'], minus_di_trend['persistence_periods']),
                'description': f'趋势减弱：+DI和-DI均持续下跌，市场可能进入震荡'
            }
        
        return {
            'signal_type': 'neutral',
            'signal_strength': 0,
            'persistence_periods': 0,
            'description': '无明显持续性趋势信号'
        }
    
    def analyze_di_trend(di_values, min_periods):
        """分析DI趋势"""
        if len(di_values) < min_periods + 1:
            return {
                'direction': 'unknown',
                'persistence_periods': 0,
                'total_change': 0
            }
        
        # 检查上升趋势
        rising_periods = 0
        for i in range(len(di_values) - min_periods, len(di_values)):
            if i > 0 and di_values[i] > di_values[i-1]:
                rising_periods += 1
            else:
                break
        
        # 检查下降趋势
        falling_periods = 0
        for i in range(len(di_values) - min_periods, len(di_values)):
            if i > 0 and di_values[i] < di_values[i-1]:
                falling_periods += 1
            else:
                break
        
        if rising_periods >= min_periods:
            total_change = di_values[-1] - di_values[-min_periods-1]
            return {
                'direction': 'rising',
                'persistence_periods': rising_periods,
                'total_change': abs(total_change)
            }
        elif falling_periods >= min_periods:
            total_change = di_values[-min_periods-1] - di_values[-1]
            return {
                'direction': 'falling',
                'persistence_periods': falling_periods,
                'total_change': abs(total_change)
            }
        else:
            return {
                'direction': 'sideways',
                'persistence_periods': 0,
                'total_change': 0
            }
    
    # 测试场景1：强烈空头信号
    print("\n📉 测试场景1：强烈空头信号")
    print("   条件：ADX持续上升 + +DI持续下跌 + -DI持续上升")
    
    adx_bearish = [20, 22, 25, 28, 30, 32]  # ADX持续上升
    plus_di_bearish = [25, 23, 21, 19, 17, 15]  # +DI持续下跌
    minus_di_bearish = [15, 17, 19, 21, 23, 25]  # -DI持续上升
    
    result_bearish = mock_check_adx_trend_persistence(adx_bearish, plus_di_bearish, minus_di_bearish)
    print(f"   结果：{result_bearish['signal_type']}")
    print(f"   强度：{result_bearish['signal_strength']}")
    print(f"   描述：{result_bearish['description']}")
    
    if result_bearish['signal_type'] == 'strong_bearish':
        print("   ✅ 正确识别强烈空头信号")
    else:
        print("   ❌ 未能正确识别强烈空头信号")
    
    # 测试场景2：强烈多头信号
    print("\n📈 测试场景2：强烈多头信号")
    print("   条件：ADX持续上升 + +DI持续上升 + -DI持续下跌")
    
    adx_bullish = [20, 22, 25, 28, 30, 32]  # ADX持续上升
    plus_di_bullish = [15, 17, 19, 21, 23, 25]  # +DI持续上升
    minus_di_bullish = [25, 23, 21, 19, 17, 15]  # -DI持续下跌
    
    result_bullish = mock_check_adx_trend_persistence(adx_bullish, plus_di_bullish, minus_di_bullish)
    print(f"   结果：{result_bullish['signal_type']}")
    print(f"   强度：{result_bullish['signal_strength']}")
    print(f"   描述：{result_bullish['description']}")
    
    if result_bullish['signal_type'] == 'strong_bullish':
        print("   ✅ 正确识别强烈多头信号")
    else:
        print("   ❌ 未能正确识别强烈多头信号")
    
    # 测试场景3：趋势减弱信号
    print("\n📊 测试场景3：趋势减弱信号")
    print("   条件：+DI和-DI均持续下跌")
    
    adx_weak = [30, 28, 26, 25, 24, 23]  # ADX下降
    plus_di_weak = [25, 23, 21, 19, 17, 15]  # +DI持续下跌
    minus_di_weak = [20, 18, 16, 14, 12, 10]  # -DI也持续下跌
    
    result_weak = mock_check_adx_trend_persistence(adx_weak, plus_di_weak, minus_di_weak)
    print(f"   结果：{result_weak['signal_type']}")
    print(f"   强度：{result_weak['signal_strength']}")
    print(f"   描述：{result_weak['description']}")
    
    if result_weak['signal_type'] == 'weakening_trend':
        print("   ✅ 正确识别趋势减弱信号")
    else:
        print("   ❌ 未能正确识别趋势减弱信号")
    
    return result_bearish['signal_type'] == 'strong_bearish' and result_bullish['signal_type'] == 'strong_bullish'

def test_ai_integration():
    """测试AI集成"""
    print("\n🤖 测试AI集成...")
    
    try:
        import main_window
        print("✅ 主程序导入成功")
        
        # 检查ADX相关方法
        main_class = main_window.MainWindow
        
        methods_to_check = [
            'check_adx_trend_persistence',
            'analyze_market_with_ai',
            '_analyze_di_trend',
            '_evaluate_persistence_signal'
        ]
        
        for method in methods_to_check:
            if hasattr(main_class, method):
                print(f"✅ {method} 方法存在")
            else:
                print(f"❌ {method} 方法缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ AI集成测试失败: {str(e)}")
        return False

def check_adx_strategy_implementation():
    """检查ADX策略实现"""
    print("\n🔍 检查ADX策略实现...")
    
    try:
        with open('main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键策略实现
        checks = [
            {
                'pattern': 'strong_bearish',
                'description': '强烈空头信号检测',
                'found': 'strong_bearish' in content
            },
            {
                'pattern': 'strong_bullish', 
                'description': '强烈多头信号检测',
                'found': 'strong_bullish' in content
            },
            {
                'pattern': 'ADX持续上升.*期，\\+DI持续下跌.*期，-DI持续上升.*期',
                'description': '空头策略描述',
                'found': 'ADX持续上升' in content and '+DI持续下跌' in content and '-DI持续上升' in content
            },
            {
                'pattern': 'ADX持续上升.*期，\\+DI持续上升.*期，-DI持续下跌.*期',
                'description': '多头策略描述', 
                'found': 'ADX持续上升' in content and '+DI持续上升' in content and '-DI持续下跌' in content
            },
            {
                'pattern': 'trend_strength.*signal_strength',
                'description': 'AI交易决策中的信号强度应用',
                'found': 'trend_strength' in content and 'signal_strength' in content
            }
        ]
        
        all_found = True
        for check in checks:
            if check['found']:
                print(f"✅ {check['description']}")
            else:
                print(f"❌ {check['description']}")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 ADX策略在AI交易中的应用测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试ADX策略逻辑
    strategy_ok = test_adx_strategy_logic()
    
    # 测试AI集成
    ai_ok = test_ai_integration()
    
    # 检查策略实现
    impl_ok = check_adx_strategy_implementation()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"   ADX策略逻辑: {'✅ 正确' if strategy_ok else '❌ 错误'}")
    print(f"   AI集成: {'✅ 正常' if ai_ok else '❌ 异常'}")
    print(f"   策略实现: {'✅ 完整' if impl_ok else '❌ 不完整'}")
    
    if strategy_ok and ai_ok and impl_ok:
        print("\n🎉 ADX策略已正确应用于AI交易！")
        print("\n📝 策略特点:")
        print("✅ 1. 当ADX指标与+DI线持续下跌，-DI线持续上升时，识别为空头趋势")
        print("✅ 2. 当ADX指标与+DI线持续上涨，-DI线持续下跌时，识别为多头趋势")
        print("✅ 3. 信号强度会影响AI交易决策的趋势强度计算")
        print("✅ 4. 支持做空和做多策略的自动执行")
        print("✅ 5. 包含趋势减弱的风险控制机制")
        
        print("\n🚀 使用建议:")
        print("• 确保ADX阈值设置合理（默认25）")
        print("• 关注信号强度阈值（默认5.0）")
        print("• 结合其他技术指标确认信号")
        print("• 在强趋势中适当调整仓位大小")
        
    else:
        print("\n⚠️  部分功能可能需要优化")
        if not strategy_ok:
            print("• ADX策略逻辑需要检查")
        if not ai_ok:
            print("• AI集成需要修复")
        if not impl_ok:
            print("• 策略实现需要完善")

if __name__ == "__main__":
    main()
