#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ADX趋势持续性功能测试脚本

测试新增的ADX趋势持续性检测功能，验证：
1. ADX、+DI、-DI持续性趋势检测
2. 强烈多头/空头信号生成
3. 信号强度计算
4. 配置参数加载
"""

import sys
import os
import json
import numpy as np
import talib
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_adx_persistence_algorithm():
    """测试ADX趋势持续性算法"""
    print("=== ADX趋势持续性算法测试 ===\n")
    
    # 模拟测试数据
    test_cases = [
        {
            'name': '强烈空头信号测试',
            'adx_values': [20, 22, 25, 28, 30, 32, 35, 38, 40, 42],  # ADX持续上升
            'plus_di_values': [25, 24, 22, 20, 18, 16, 14, 12, 10, 8],  # +DI持续下跌
            'minus_di_values': [15, 17, 19, 22, 25, 28, 30, 32, 35, 38],  # -DI持续上升
            'expected_signal': 'strong_bearish'
        },
        {
            'name': '强烈多头信号测试',
            'adx_values': [18, 20, 23, 26, 29, 32, 35, 38, 41, 44],  # ADX持续上升
            'plus_di_values': [12, 15, 18, 21, 24, 27, 30, 33, 36, 39],  # +DI持续上升
            'minus_di_values': [28, 26, 24, 21, 18, 15, 12, 10, 8, 6],  # -DI持续下跌
            'expected_signal': 'strong_bullish'
        },
        {
            'name': '趋势减弱信号测试',
            'adx_values': [35, 34, 32, 30, 28, 26, 24, 22, 20, 18],  # ADX下降
            'plus_di_values': [25, 23, 21, 19, 17, 15, 13, 11, 9, 7],  # +DI下降
            'minus_di_values': [20, 18, 16, 14, 12, 10, 8, 6, 4, 2],  # -DI下降
            'expected_signal': 'weakening_trend'
        },
        {
            'name': '数据不足测试',
            'adx_values': [25, 27],  # 数据不足
            'plus_di_values': [20, 22],
            'minus_di_values': [15, 17],
            'expected_signal': 'insufficient_data'
        }
    ]
    
    # 模拟ADX持续性检测函数
    def mock_check_adx_trend_persistence(adx_values, plus_di_values, minus_di_values, min_periods=3):
        """模拟ADX趋势持续性检测"""
        if len(adx_values) < min_periods + 1:
            return {
                'signal_type': 'insufficient_data',
                'signal_strength': 0,
                'persistence_periods': 0,
                'description': '数据不足，无法判断趋势持续性'
            }
        
        # 检查ADX上升趋势
        adx_rising_periods = 0
        for i in range(len(adx_values) - min_periods, len(adx_values)):
            if i > 0 and adx_values[i] > adx_values[i-1]:
                adx_rising_periods += 1
            else:
                break
        
        # 检查+DI趋势
        plus_di_trend = analyze_di_trend(plus_di_values, min_periods)
        minus_di_trend = analyze_di_trend(minus_di_values, min_periods)
        
        # 当前值
        current_adx = adx_values[-1]
        current_plus_di = plus_di_values[-1]
        current_minus_di = minus_di_values[-1]
        
        # 判断信号类型
        if (adx_rising_periods >= min_periods and 
            plus_di_trend['direction'] == 'falling' and 
            minus_di_trend['direction'] == 'rising' and
            current_minus_di > current_plus_di and
            current_adx >= 25):
            
            signal_strength = min(plus_di_trend['persistence_periods'], minus_di_trend['persistence_periods']) + adx_rising_periods
            return {
                'signal_type': 'strong_bearish',
                'signal_strength': signal_strength,
                'persistence_periods': min(plus_di_trend['persistence_periods'], minus_di_trend['persistence_periods']),
                'description': f'强烈空头信号：ADX持续上升{adx_rising_periods}期，+DI持续下跌{plus_di_trend["persistence_periods"]}期，-DI持续上升{minus_di_trend["persistence_periods"]}期'
            }
        
        elif (adx_rising_periods >= min_periods and 
              plus_di_trend['direction'] == 'rising' and 
              minus_di_trend['direction'] == 'falling' and
              current_plus_di > current_minus_di and
              current_adx >= 25):
            
            signal_strength = min(plus_di_trend['persistence_periods'], minus_di_trend['persistence_periods']) + adx_rising_periods
            return {
                'signal_type': 'strong_bullish',
                'signal_strength': signal_strength,
                'persistence_periods': min(plus_di_trend['persistence_periods'], minus_di_trend['persistence_periods']),
                'description': f'强烈多头信号：ADX持续上升{adx_rising_periods}期，+DI持续上升{plus_di_trend["persistence_periods"]}期，-DI持续下跌{minus_di_trend["persistence_periods"]}期'
            }
        
        elif (plus_di_trend['direction'] == 'falling' and minus_di_trend['direction'] == 'falling'):
            return {
                'signal_type': 'weakening_trend',
                'signal_strength': -1,
                'persistence_periods': min(plus_di_trend['persistence_periods'], minus_di_trend['persistence_periods']),
                'description': f'趋势减弱：+DI和-DI均持续下跌，市场可能进入震荡'
            }
        
        else:
            return {
                'signal_type': 'neutral',
                'signal_strength': 0,
                'persistence_periods': 0,
                'description': f'趋势持续性不明确'
            }
    
    def analyze_di_trend(di_values, min_periods, min_change_threshold=0.5):
        """分析DI线趋势"""
        trend_info = {
            'direction': 'neutral',
            'persistence_periods': 0,
            'total_change': 0
        }
        
        if len(di_values) < min_periods + 1:
            return trend_info
        
        # 检查持续上升
        rising_periods = 0
        rising_change = 0
        for i in range(len(di_values) - min_periods, len(di_values)):
            if i > 0 and di_values[i] > di_values[i-1]:
                rising_periods += 1
                rising_change += (di_values[i] - di_values[i-1])
            else:
                break
        
        # 检查持续下降
        falling_periods = 0
        falling_change = 0
        for i in range(len(di_values) - min_periods, len(di_values)):
            if i > 0 and di_values[i] < di_values[i-1]:
                falling_periods += 1
                falling_change += (di_values[i-1] - di_values[i])
            else:
                break
        
        if rising_periods >= min_periods and rising_change >= min_change_threshold:
            trend_info['direction'] = 'rising'
            trend_info['persistence_periods'] = rising_periods
            trend_info['total_change'] = rising_change
        elif falling_periods >= min_periods and falling_change >= min_change_threshold:
            trend_info['direction'] = 'falling'
            trend_info['persistence_periods'] = falling_periods
            trend_info['total_change'] = falling_change
        
        return trend_info
    
    # 执行测试
    all_passed = True
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试 {i}: {test_case['name']}")
        print(f"ADX值: {test_case['adx_values']}")
        print(f"+DI值: {test_case['plus_di_values']}")
        print(f"-DI值: {test_case['minus_di_values']}")
        
        result = mock_check_adx_trend_persistence(
            test_case['adx_values'],
            test_case['plus_di_values'],
            test_case['minus_di_values']
        )
        
        print(f"检测结果: {result['signal_type']}")
        print(f"信号强度: {result['signal_strength']}")
        print(f"描述: {result['description']}")
        
        if result['signal_type'] == test_case['expected_signal']:
            print("✅ 测试通过")
        else:
            print(f"❌ 测试失败 - 期望: {test_case['expected_signal']}, 实际: {result['signal_type']}")
            all_passed = False
        
        print("-" * 60)
    
    return all_passed

def test_config_loading():
    """测试配置文件加载"""
    print("\n=== 配置文件加载测试 ===\n")
    
    try:
        # 检查trading_settings.json是否存在
        if os.path.exists('trading_settings.json'):
            with open('trading_settings.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print("✅ trading_settings.json 加载成功")
            
            # 检查ADX配置
            tech_settings = config.get('tech_settings', {})
            adx_settings = tech_settings.get('adx', {})
            
            if adx_settings:
                print("✅ ADX配置存在")
                print(f"   周期: {adx_settings.get('period', 'N/A')}")
                print(f"   阈值: {adx_settings.get('threshold', 'N/A')}")
                print(f"   强阈值: {adx_settings.get('strong_threshold', 'N/A')}")
                
                persistence_config = adx_settings.get('persistence', {})
                if persistence_config:
                    print("✅ ADX持续性配置存在")
                    print(f"   最小周期: {persistence_config.get('min_periods', 'N/A')}")
                    print(f"   DI变化阈值: {persistence_config.get('min_di_change_threshold', 'N/A')}")
                    print(f"   信号强度阈值: {persistence_config.get('signal_strength_threshold', 'N/A')}")
                    print(f"   启用持续性信号: {persistence_config.get('enable_persistence_signals', 'N/A')}")
                    return True
                else:
                    print("❌ ADX持续性配置缺失")
                    return False
            else:
                print("❌ ADX配置缺失")
                return False
        else:
            print("❌ trading_settings.json 文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 配置加载失败: {str(e)}")
        return False

def test_real_market_data():
    """使用真实市场数据测试ADX计算"""
    print("\n=== 真实市场数据测试 ===\n")
    
    try:
        # 生成模拟的真实市场数据
        np.random.seed(42)  # 确保结果可重现
        
        # 模拟价格数据（上涨趋势）
        base_price = 50000
        prices = []
        for i in range(50):
            # 添加趋势和随机波动
            trend = i * 10  # 上涨趋势
            noise = np.random.normal(0, 100)  # 随机波动
            price = base_price + trend + noise
            prices.append(max(price, base_price * 0.8))  # 防止价格过低
        
        # 生成OHLC数据
        high_prices = np.array([p * (1 + abs(np.random.normal(0, 0.01))) for p in prices])
        low_prices = np.array([p * (1 - abs(np.random.normal(0, 0.01))) for p in prices])
        close_prices = np.array(prices)
        
        print(f"生成了{len(close_prices)}个价格点")
        print(f"价格范围: ${low_prices.min():.2f} - ${high_prices.max():.2f}")
        
        # 计算ADX指标
        adx_period = 14
        adx = talib.ADX(high_prices, low_prices, close_prices, timeperiod=adx_period)
        plus_di = talib.PLUS_DI(high_prices, low_prices, close_prices, timeperiod=adx_period)
        minus_di = talib.MINUS_DI(high_prices, low_prices, close_prices, timeperiod=adx_period)
        
        # 检查计算结果
        if len(adx) > 0 and not np.isnan(adx[-1]):
            current_adx = round(float(adx[-1]), 2)
            current_plus_di = round(float(plus_di[-1]), 2)
            current_minus_di = round(float(minus_di[-1]), 2)
            
            print(f"✅ ADX计算成功")
            print(f"   当前ADX: {current_adx}")
            print(f"   +DI: {current_plus_di}")
            print(f"   -DI: {current_minus_di}")
            
            # 判断趋势
            if current_plus_di > current_minus_di:
                trend = "看多"
            elif current_minus_di > current_plus_di:
                trend = "看空"
            else:
                trend = "中性"
            
            # 判断强度
            if current_adx >= 40:
                strength = "极强"
            elif current_adx >= 25:
                strength = "强"
            else:
                strength = "弱"
            
            print(f"   趋势方向: {trend}")
            print(f"   趋势强度: {strength}")
            
            return True
        else:
            print("❌ ADX计算失败: 结果为NaN或空")
            return False
            
    except Exception as e:
        print(f"❌ 真实市场数据测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 ADX趋势持续性功能测试开始\n")
    print("=" * 80)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("ADX趋势持续性算法", test_adx_persistence_algorithm()))
    test_results.append(("配置文件加载", test_config_loading()))
    test_results.append(("真实市场数据", test_real_market_data()))
    
    # 汇总测试结果
    print("\n" + "=" * 80)
    print("📊 测试结果汇总")
    print("=" * 80)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed_count += 1
    
    print(f"\n总计: {passed_count}/{len(test_results)} 项测试通过")
    
    if passed_count == len(test_results):
        print("🎉 所有测试通过！ADX趋势持续性功能可以正常使用。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
