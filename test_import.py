#!/usr/bin/env python3
"""
测试导入脚本
用于验证main_window.py是否能正常导入和运行
"""

import sys
import os

def test_imports():
    """测试所有必要的导入"""
    print("🔍 开始测试导入...")
    
    # 测试基础PyQt6组件
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow
        print("✅ PyQt6基础组件导入成功")
    except ImportError as e:
        print(f"❌ PyQt6基础组件导入失败: {e}")
        return False
    
    # 测试PyQt6-Charts（可选）
    try:
        from PyQt6.QtCharts import QChart, QChartView
        print("✅ PyQt6-Charts导入成功")
        charts_available = True
    except ImportError as e:
        print(f"⚠️  PyQt6-Charts导入失败: {e}")
        print("   程序将使用回退模式")
        charts_available = False
    
    # 测试其他依赖
    dependencies = [
        ('ccxt', 'ccxt'),
        ('pandas', 'pd'),
        ('talib', 'talib'),
        ('requests', 'requests'),
        ('dotenv', 'load_dotenv')
    ]
    
    for dep_name, import_name in dependencies:
        try:
            if import_name == 'load_dotenv':
                from dotenv import load_dotenv
            else:
                __import__(dep_name)
            print(f"✅ {dep_name} 导入成功")
        except ImportError as e:
            print(f"⚠️  {dep_name} 导入失败: {e}")
    
    # 测试主程序导入
    try:
        import main_window
        print("✅ main_window.py 导入成功")
        return True
    except ImportError as e:
        print(f"❌ main_window.py 导入失败: {e}")
        return False

def test_gui_creation():
    """测试GUI创建"""
    print("\n🖥️  测试GUI创建...")
    
    # 设置无头模式（避免显示窗口）
    os.environ["QT_QPA_PLATFORM"] = "offscreen"
    
    try:
        from PyQt6.QtWidgets import QApplication
        import main_window
        
        app = QApplication(sys.argv)
        window = main_window.MainWindow()
        print("✅ GUI窗口创建成功")
        
        # 测试一些基本方法
        if hasattr(window, 'log_trading'):
            window.log_trading("测试日志消息")
            print("✅ 日志功能正常")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ GUI创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试币安量化交易机器人...")
    print("=" * 50)
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        print("\n" + "=" * 50)
        # 测试GUI创建
        gui_success = test_gui_creation()
        
        print("\n" + "=" * 50)
        print("📋 测试结果总结:")
        print(f"   导入测试: {'✅ 通过' if import_success else '❌ 失败'}")
        print(f"   GUI测试: {'✅ 通过' if gui_success else '❌ 失败'}")
        
        if import_success and gui_success:
            print("\n🎉 所有测试通过！程序可以正常运行")
            print("\n📝 运行程序:")
            print("   python main_window.py")
        else:
            print("\n⚠️  部分测试失败，但程序可能仍能运行")
            print("   请尝试运行: python main_window.py")
    else:
        print("\n❌ 基础导入失败，请先修复依赖问题")
        print("   运行修复脚本: python fix_pyqt6.py")

if __name__ == "__main__":
    main()
